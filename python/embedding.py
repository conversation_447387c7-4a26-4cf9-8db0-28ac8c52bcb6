# Requires transformers>=4.36.0
import numpy as np
import onnxruntime as ort
from transformers import AutoTokenizer

input_texts = ["580分左右能上哪个大学", "5万以内的纯电车"]
onnx_path = "/feng/Projects/RustProjects/tokenizer_wrapper/go_binding/inference/onnx/gte-ft-v3.0.onnx"
model_name_or_path = "Alibaba-NLP/gte-multilingual-base"
tokenizer = AutoTokenizer.from_pretrained(model_name_or_path)
ort_session = ort.InferenceSession(
    onnx_path, providers=[("CUDAExecutionProvider", {"device_id": 1})]
)

# Tokenize the input texts
batch_dict = tokenizer(
    input_texts, max_length=512, padding=True, truncation=True, return_tensors="np"
)

input = {}
for key, val in batch_dict.items():
    input[key] = val

outputs = ort_session.run(["logits"], input_feed=input)[0]
outputs = outputs[:,0,:]
l2_norm = np.linalg.norm(outputs)
outputs = outputs / l2_norm

for output in outputs:
    print(output.tolist()[:15])
