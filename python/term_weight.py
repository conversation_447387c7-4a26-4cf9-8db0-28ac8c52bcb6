# coding=utf-8
import numpy
import onnxruntime
from transformers import <PERSON><PERSON><PERSON><PERSON>
from collections import Counter
from typing import List
import time


def numpy_lst_padding(lst, padding_value):
    max_len = max([len(items) for items in lst])
    pad_ar = numpy.full(
        [len(lst), max_len] + list(lst[0].shape)[1:], padding_value, dtype=lst[0].dtype
    )
    for i, ar in enumerate(lst):
        pad_ar[i, : len(ar)] = ar

    return pad_ar


def seq_pad(ar, expand_len, padding_value):
    pad_ar = numpy.full(
        list(ar.shape)[:-1] + [expand_len], padding_value, dtype=ar.dtype
    )
    pad_ar[..., : ar.shape[-1]] = ar

    return pad_ar


class TermWeightPredictor(object):
    def __init__(self, model_dir: str, device_id: int = 0, max_query_length: int = 128):

        self.max_query_length = max_query_length
        self.tokenizer: BertTokenizer = BertTokenizer.from_pretrained(
            f"{model_dir}/tokenizer", do_lower_case=True
        )

        providers = (
            ["CPUExecutionProvider"]
            if device_id < 0
            else ([("CUDAExecutionProvider", {"device_id": device_id})])
        )
        self.ort_session = onnxruntime.InferenceSession(
            f"{model_dir}/twbert.onnx", providers=providers
        )

    def numpy_infer_mask_query_with_token(self, terms: List[str]):

        terms_t = [
            numpy.squeeze(
                self.tokenizer(
                    term, add_special_tokens=False, return_tensors="np"
                ).input_ids,
                0,
            )
            for term in terms
        ]
        terms_t_lens = numpy.array(
            [len(term_t) for term_t in terms_t], dtype=numpy.int64
        )
        cumsum_lens = numpy.cumsum(terms_t_lens, axis=0)
        # 找到累积和大于阈值的索引
        indices = numpy.nonzero(cumsum_lens <= self.max_query_length)[0]
        # 获取第一个符合条件的索引
        max_term_l = indices[-1] + 1 if len(indices) > 0 else len(terms)

        query_input_ids = {}
        query_input_ids["input_ids"] = numpy.concatenate(
            [
                numpy.array([self.tokenizer.cls_token_id]),
                numpy.concatenate(terms_t[:max_term_l], axis=0),
                numpy.array([self.tokenizer.sep_token_id]),
            ],
            axis=0,
        )
        query_input_ids["attention_mask"] = numpy.ones(
            query_input_ids["input_ids"].shape[0], dtype=numpy.int64
        )
        query_input_ids["token_type_ids"] = numpy.zeros(
            query_input_ids["input_ids"].shape[0], dtype=numpy.int64
        )

        terms_mask = numpy.zeros(
            (len(terms_t), query_input_ids["input_ids"].shape[0]), dtype=numpy.float32
        )
        cumsum_lens = [0] + cumsum_lens.tolist()
        for i in range(1, len(cumsum_lens)):
            # 所有indice都要加1因为有cls_token未计入
            terms_mask[i - 1, cumsum_lens[i - 1] + 1 : cumsum_lens[i] + 1] = 1

        terms = terms[:max_term_l]
        terms_mask = terms_mask[:max_term_l]
        terms_t_lens = terms_t_lens[:max_term_l]

        return query_input_ids, terms, terms_mask, terms_t_lens

    def run(self, terms_lst):

        query_input_ids_lst, terms_lst, terms_mask_lst, terms_t_lens_lst = zip(
            *[self.numpy_infer_mask_query_with_token(terms) for terms in terms_lst]
        )

        input_ids_lst, input_mask_lst, token_type_ids_lst = zip(
            *[
                [
                    query_input_ids["input_ids"],
                    query_input_ids["attention_mask"],
                    query_input_ids["token_type_ids"],
                ]
                for query_input_ids in query_input_ids_lst
            ]
        )
        # input_ids_lst,token_type_ids_lst,input_mask_lst=[[numpy.expand_dims(ar,0) for ar in lst] for lst in [input_ids_lst,token_type_ids_lst,input_mask_lst]]

        # padding
        input_ids, input_mask, token_type_ids = list(
            map(
                lambda lst: numpy_lst_padding(
                    lst, padding_value=self.tokenizer.pad_token_id
                ),
                [input_ids_lst, input_mask_lst, token_type_ids_lst],
            )
        )
        terms_mask_lst = list(
            map(
                lambda x: seq_pad(x, max(ar.shape[-1] for ar in terms_mask_lst), 0),
                terms_mask_lst,
            )
        )
        terms_mask_t = numpy_lst_padding(terms_mask_lst, padding_value=0)
        # terms_tf_t=numpy_lst_padding(list(map(lambda x:numpy.array(x),terms_tf_lst)), padding_value=0)
        # 对长度padding不能为0，terms的长度是作为除数的
        terms_t_lens_t = numpy_lst_padding(
            list(map(lambda x: numpy.array(x), terms_t_lens_lst)), padding_value=1
        )

        (term_weights,) = self.ort_session.run(
            None,
            {
                "input_ids": input_ids,
                "input_mask": input_mask,
                "token_type_ids": token_type_ids,
                "terms_mask": terms_mask_t,
                "terms_t_lens": terms_t_lens_t,
            },
        )

        weight_maps = [
            {term: weight / sum(tw) for term, weight in zip(terms, tw)}
            for terms, tw in zip(terms_lst, term_weights)
        ]

        return weight_maps


def main() -> None:
    model_dir = "/feng/IflySearch_Resource/TermWeight/v20231221"
    termweight = TermWeightPredictor(
        model_dir=model_dir, device_id=-1, max_query_length=128
    )

    querys = [
        "滨海新区融媒体中心编委会副主任、新媒体编辑部主任冯伟",
        "小学信息科技学科的跨学科融合举例子",
        "湖南郴州张逸宽是谁",
        "湿寒气重怎么治疗",
    ]
    terms_lst = [
        ["滨海新区融媒体中心编委会", "副主任", "新媒体", "编辑部", "主任", "冯伟"],
        ["小学", "信息", "科技", "学科", "跨学科", "融合", "举例子"],
        ["湖南", "郴州", "张逸宽", "谁"],
    ]

    terms_tf_dict_lst = [Counter(terms) for terms in terms_lst]

    now = time.time()
    preds = termweight.run(terms_lst)
    print(preds)
    print(f"total time: {time.time() - now}")


if __name__ == "__main__":
    main()
