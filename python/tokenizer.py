from transformers import AutoTokenizer

if __name__ == "__main__":
    # tokenizer = AutoTokenizer.from_pretrained("./roformer-sim-small-chinese")
    # tokenizer = AutoTokenizer.from_pretrained("WangZeJun/roformer-sim-small-chinese")
    # tokenizer = AutoTokenizer.from_pretrained("BAAI/bge-m3")
    # tokenizer = AutoTokenizer.from_pretrained("BAAI/bge-base-zh-v1.5")
    tokenizer = AutoTokenizer.from_pretrained("Xenova/ernie-3.0-nano-zh")
    tokenizer.save_pretrained("./ernie-3.0-nano-zh")
    query_list = ["白日依山尽", "欲穷千里目"]
    answer_list = ["黄河入海流", "更上一层楼"]
    output = tokenizer(
        query_list, answer_list, padding=True, truncation=True, max_length=512
    )

    for id, attention_mask in zip(output["input_ids"], output["attention_mask"]):
        print(f"id: {id}\nattention_mask: {attention_mask}\n")

    # tokenizer.save_pretrained("roformer-sim-small-chinese")
