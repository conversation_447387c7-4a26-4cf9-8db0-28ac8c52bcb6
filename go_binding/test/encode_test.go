package test

import (
	"fmt"
	"log"
	"testing"

	wrapper "git.iflytek.com/RS_DataHub/ifly_search_common/tokenizer_v2"
)

func TestRemote(t *testing.T) {
	// 加载动态库
	libHandle, err := wrapper.LoadEngineLib("/feng/IflySearch_Resource/ort_lib/libtokenizer_wrapper.so")
	if err != nil {
		log.Fatalf("load lib failed: %v", err)
	}
	defer wrapper.CloseEngineLib(libHandle)

	// modelName := "Xenova/ernie-3.0-nano-zh"
	// modelName := "bert-base-uncased"
	modelName := "BAAI/bge-m3"
	// modelName := "BAAI/bge-base-zh-v1.5"
	useLocal, padId, padFixed, maxLength := 0, 0, 0, 512
	tokenizer := wrapper.NewTokenizerWrapper(modelName, useLocal, padId, padFixed, maxLength)

	queries := []string{"白日依山尽", "欲穷千里目"}
	answers := []string{"黄河入海流", "更上一层楼"}

	encodings, _ := tokenizer.EncodeBatchPair(queries, answers, true)

	fmt.Printf("%+v\n", encodings)
}
