#include "tokenizer_wrapper.h"

// 函数名称符号
const char *createSimpleTokenizerSym = "create_simple_tokenizer";
const char *withTruncationSym = "with_truncation";
const char *withPaddingSym = "with_padding";
const char *createTokenizerSym = "create_tokenizer";
const char *encodeBatchSym = "encode_batch";
const char *encodeBatchPairSym = "encode_batch_pair";
const char *freeStringSym = "free_string";

// 函数指针
typedef void *(*createSimpleTokenizerFunc)(const char *model_name, int use_local);
typedef void *(*withTruncationFunc)(void *wrapper, size_t max_length);
typedef void *(*withPaddingFunc)(void *wrapper, unsigned int pad_id, int pad_fix, size_t max_length);
typedef void *(*createTokenizerFunc)(const char *model_name, int use_local, unsigned int pad_id, int pad_fix, size_t max_length);
typedef char *(*encodeBatchFunc)(void *wrapper, const char **queries, size_t query_len, int add_special_tokens);
typedef char *(*encodeBatchPairFunc)(void *wrapper, const char **queries, size_t query_len, const char **answers, size_t answer_len, int add_special_tokens);
typedef void (*freeStringFunc)(char *s);

// 函数句柄
createSimpleTokenizerFunc cPtrCreateSimpleTokenizer;
withTruncationFunc cPtrWithTruncation;
withPaddingFunc cPtrWithPadding;
createTokenizerFunc cPtrCreateTokenizer;
encodeBatchFunc cPtrEncodeBatch;
encodeBatchPairFunc cPtrEncodeBatchPair;
freeStringFunc cPtrFreeString;

// 动态调用C库
// @return library handle
void *cLibOpen(const char *libName, char **err)
{
    void *hdl = dlopen(libName, RTLD_NOW);
    if (hdl == NULL)
    {
        *err = (char *)dlerror();
    }
    return hdl;
}

// 加载符号地址
// @return symbol address
void *cLibLoad(void *hdl, const char *sym, char **err)
{
    void *addr = dlsym(hdl, sym);
    if (addr == NULL)
    {
        *err = (char *)dlerror();
    }
    return addr;
}

int cLibClose(void *hdl)
{
    int ret = dlclose(hdl);
    if (ret != 0)
        return -1;
    return 0;
}

// 加载c库并初始化函数指针
const char *loadWrapperSym(void *hdl, char **loadErr)
{
    // 加载函数名为Sym的地址，并转化为Func类型函数指针
    if ((cPtrCreateSimpleTokenizer = cLibLoad(hdl, createSimpleTokenizerSym, loadErr)) == NULL)
        return createSimpleTokenizerSym;

    if ((cPtrWithTruncation = cLibLoad(hdl, withTruncationSym, loadErr)) == NULL)
        return withTruncationSym;

    if ((cPtrWithPadding = cLibLoad(hdl, withPaddingSym, loadErr)) == NULL)
        return withPaddingSym;

    if ((cPtrCreateTokenizer = cLibLoad(hdl, createTokenizerSym, loadErr)) == NULL)
        return createTokenizerSym;

    if ((cPtrEncodeBatch = cLibLoad(hdl, encodeBatchSym, loadErr)) == NULL)
        return encodeBatchSym;

    if ((cPtrEncodeBatchPair = cLibLoad(hdl, encodeBatchPairSym, loadErr)) == NULL)
        return encodeBatchPairSym;

    if ((cPtrFreeString = cLibLoad(hdl, freeStringSym, loadErr)) == NULL)
        return freeStringSym;

    return NULL;
}

// 接口适配，调用c库
void *callCreateSimpleTokenizer(const char *model_name, int use_local)
{
    return cPtrCreateSimpleTokenizer(model_name, use_local);
}

// 接口适配，调用c库
void *callWithTruncation(void *wrapper, size_t max_length)
{
    return cPtrWithTruncation(wrapper, max_length);
}

// 接口适配，调用c库
void *callWithPadding(void *wrapper, unsigned int pad_id, int pad_fix, size_t max_length)
{
    return cPtrWithPadding(wrapper, pad_id, pad_fix, max_length);
}

// 接口适配，调用c库
void *callCreateTokenizer(const char *model_name, int use_local, unsigned int pad_id, int pad_fix, size_t max_length)
{
    return cPtrCreateTokenizer(model_name, use_local, pad_id, pad_fix, max_length);
}

// 接口适配，调用c库
char *callEncodeBatch(void *wrapper, const char **queries, size_t query_len, int add_special_tokens)
{
    return cPtrEncodeBatch(wrapper, queries, query_len, add_special_tokens);
}

// 接口适配，调用c库
char *callEncodeBatchPair(void *wrapper, const char **queries, size_t query_len, const char **answers, size_t answer_len, int add_special_tokens)
{
    return cPtrEncodeBatchPair(wrapper, queries, query_len, answers, answer_len, add_special_tokens);
}

// 接口适配，调用c库
void callFreeString(char *s)
{
    return cPtrFreeString(s);
}
