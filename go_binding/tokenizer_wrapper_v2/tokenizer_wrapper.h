#ifndef TOKENIZER_WRAPPER_H
#define TOKENIZER_WRAPPER_H

#include <dlfcn.h>
#include <stdlib.h>

// 所有的函数声明和定义
int cLibClose(void *hdl);
void *cLibOpen(const char *libName, char **err);
void *cLibLoad(void *hdl, const char *sym, char **err);

const char *loadWrapperSym(void *hdl, char **loadErr);
void *callCreateSimpleTokenizer(const char *model_name, int use_local);
void *callWithTruncation(void *wrapper, size_t max_length);
void *callWithPadding(void *wrapper, unsigned int pad_id, int pad_fix, size_t max_length);
void *callCreateTokenizer(const char *model_name, int use_local, unsigned int pad_id, int pad_fix, size_t max_length);
char *callEncodeBatch(void *wrapper, const char **queries, size_t query_len, int add_special_tokens);
char *callEncodeBatchPair(void *wrapper, const char **queries, size_t query_len, const char **answers, size_t answer_len, int add_special_tokens);
void callFreeString(char *s);

#endif // TOKENIZER_WRAPPER_H