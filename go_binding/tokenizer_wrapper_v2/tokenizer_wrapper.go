package tokenizerwrapperv2

/*
#cgo LDFLAGS : -ldl
#include "./tokenizer_wrapper.h"
*/
import "C"
import (
	"encoding/json"
	"fmt"
	"unsafe"
)

type TokenizerWrapper struct {
	tokenizer unsafe.Pointer
}

type Encoding struct {
	Ids         []int64   `json:"ids"`
	Types       []int64   `json:"type_ids"`
	Masks       []int64   `json:"attention_mask"`
	Tokens      []string  `json:"tokens"`
	Offsets     [][]int64 `json:"offsets"`
	SpecialMask []int64   `json:"special_tokens_mask"`
	Words       []int64   `json:"words"`
}

func (e *Encoding) String() string {
	jsonByte, _ := json.Marshal(e)
	return string(jsonByte)
}

// 加载引擎lib文件
func LoadEngineLib(libName string) (unsafe.Pointer, error) {
	var errC *C.char
	var libNameC *C.char = C.CString(libName)
	defer C.free(unsafe.Pointer(libNameC))
	defer C.free(unsafe.Pointer(errC))
	libHandle := C.cLibOpen(libNameC, &errC)
	if libHandle == nil {
		return nil, fmt.Errorf("wrapper.open: load library %s failed, %s", libName, C.GoString(errC))
	}

	if errSym := C.loadWrapperSym(libHandle, &errC); errSym != nil {
		C.cLibClose(libHandle)
		return nil, fmt.Errorf("wrapper.open: load symbol %s failed, %s", C.GoString(errSym), C.GoString(errC))
	}
	return libHandle, nil
}

// 关闭引擎lib文件
func CloseEngineLib(libHandle unsafe.Pointer) {
	C.cLibClose(libHandle)
}

// 创建并初始化 tokenizer simple
func NewSimpleTokenizer(modelName string, useLocal int) *TokenizerWrapper {
	return &TokenizerWrapper{
		tokenizer: C.callCreateSimpleTokenizer(C.CString(modelName), C.int(useLocal)),
	}
}

// 创建并初始化 tokenizerWrappers
func NewTokenizerWrapper(modelName string, useLocal, padId, padFixed, maxLength int) *TokenizerWrapper {
	return &TokenizerWrapper{
		tokenizer: C.callCreateTokenizer(C.CString(modelName), C.int(useLocal), C.uint(padId), C.int(padFixed), C.size_t(maxLength)),
	}
}

// token数截断
func (instance *TokenizerWrapper) WithTruncation(maxLength int) *TokenizerWrapper {
	C.callWithTruncation(instance.tokenizer, C.size_t(maxLength))
	return instance
}

// padding策略
func (instance *TokenizerWrapper) WithPadding(padId, padFixed, maxLength int) *TokenizerWrapper {
	C.callWithPadding(instance.tokenizer, C.uint(padId), C.int(padFixed), C.size_t(maxLength))
	return instance
}

// 文本序列编码
func (instance *TokenizerWrapper) EncodeBatch(queries []string, add_special_tokens bool) ([]*Encoding, error) {
	batchSize := len(queries)
	cQueries := make([]*C.char, batchSize)
	for i, s := range queries {
		cQueries[i] = C.CString(s)
	}

	cStr := C.callEncodeBatch(instance.tokenizer, &cQueries[0], C.size_t(batchSize), C.int(bool2int(add_special_tokens)))
	if cStr == nil {
		return nil, fmt.Errorf("failed to encode batch")
	}

	defer func() {
		for _, s := range cQueries {
			C.free(unsafe.Pointer(s))
		}
		C.callFreeString(cStr)
	}()

	var encodings []Encoding
	json.Unmarshal([]byte(C.GoString(cStr)), &encodings)

	ptrEncodings := make([]*Encoding, batchSize)
	for i := range encodings {
		ptrEncodings[i] = &encodings[i]
	}
	return ptrEncodings, nil
}

// 文本对序列编码
func (instance *TokenizerWrapper) EncodeBatchPair(queries, answers []string, add_special_tokens bool) ([]*Encoding, error) {
	batchSize := len(queries)
	if batchSize != len(answers) {
		return nil, fmt.Errorf("queries and answers must have the same length")
	}

	cQueries := make([]*C.char, batchSize)
	cAnswers := make([]*C.char, batchSize)

	for i := range queries {
		cQueries[i] = C.CString(queries[i])
		cAnswers[i] = C.CString(answers[i])
	}

	cStr := C.callEncodeBatchPair(instance.tokenizer, &cQueries[0], C.size_t(batchSize), &cAnswers[0], C.size_t(batchSize), C.int(bool2int(add_special_tokens)))
	if cStr == nil {
		return nil, fmt.Errorf("failed to encode batch pair")
	}

	defer func() {
		for i := range cQueries {
			C.free(unsafe.Pointer(cQueries[i]))
			C.free(unsafe.Pointer(cAnswers[i]))
		}
		C.callFreeString(cStr)
	}()

	var encodings []Encoding
	if err := json.Unmarshal([]byte(C.GoString(cStr)), &encodings); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON: %v", err)
	}

	ptrEncodings := make([]*Encoding, batchSize)
	for i := range encodings {
		ptrEncodings[i] = &encodings[i]
	}

	return ptrEncodings, nil
}

func bool2int(b bool) int {
	if b {
		return 1
	}
	return 0
}
