package tokenizerwrapperv2

import (
	"fmt"
	"log"
	"testing"
)

func TestEncodeBatch(t *testing.T) {
	// 加载动态库
	libHandle, err := LoadEngineLib("./lib/libtokenizer_wrapper.so")
	if err != nil {
		log.Fatalf("load lib failed: %v", err)
	}
	defer CloseEngineLib(libHandle)

	modelName := "/jfs/fengli16/IflySearch_Resource/Rank/v20250227/ernie-3.0-base-zh/tokenizer.json"
	// modelName := "bert-base-uncased"
	// modelName := "BAAI/bge-m3"
	// modelName := "BAAI/bge-base-zh-v1.5"
	useLocal, padId, padFixed, maxLength := 1, 0, 0, 512
	tokenizer := NewTokenizerWrapper(modelName, useLocal, padId, padFixed, maxLength)

	queries := []string{"白日依山尽", "欲穷千里目"}
	answers := []string{"黄河入海流", "更上一层楼"}

	encodings, _ := tokenizer.EncodeBatchPair(queries, answers, true)

	fmt.Printf("%+v\n", encodings)
}
