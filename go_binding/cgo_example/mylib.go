package main

/*
#cgo LDFLAGS : -ldl
#include <dlfcn.h>
#include <stdlib.h>
#include "./wrapper.c"
*/
import "C"
import (
	"fmt"
	"log"
	"unsafe"
)

// 加载引擎lib文件
func LoadEngineLib(libName string) (unsafe.Pointer, error) {
	var errC *C.char
	var libNameC *C.char = C.CString(libName)
	defer C.free(unsafe.Pointer(libNameC))
	defer C.free(unsafe.Pointer(errC))
	libHandle := C.cLibOpen(libNameC, &errC)
	if libHandle == nil {
		return nil, fmt.Errorf("wrapper.open: load library %s failed, %s", libName, C.GoString(errC))
	}

	if errSym := C.loadWrapperSym(libHandle, &errC); errSym != nil {
		C.cLibClose(libHandle)
		return nil, fmt.Errorf("wrapper.open: load symbol %s failed, %s", C.Go<PERSON>tring(errSym), C.GoString(errC))
	}
	return libHandle, nil
}

func main() {
	// 加载动态库
	libHandle, err := LoadEngineLib("./libmylib.so")
	if err != nil {
		log.Fatalf("load lib failed: %v", err)
	}

	// 调用C函数
	C.callHello(C.CString("New World"))
	C.cLibClose(libHandle)
}
