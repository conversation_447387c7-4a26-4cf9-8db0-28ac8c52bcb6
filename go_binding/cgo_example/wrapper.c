#include <dlfcn.h>
#include <stdlib.h>

// 函数名称符号
const char *helloSymbol = "hello";

// 函数指针
typedef void (*helloFunc)(char *name);

// 函数句柄
helloFunc cPtrHello;

// 动态调用C库
// @return library handle
void *cLibOpen(const char *libName, char **err)
{
    void *hdl = dlopen(libName, RTLD_NOW);
    if (hdl == NULL)
    {
        *err = (char *)dlerror();
    }
    return hdl;
}

// 加载符号地址
// @return symbol address
void *cLibLoad(void *hdl, const char *sym, char **err)
{
    void *addr = dlsym(hdl, sym);
    if (addr == NULL)
    {
        *err = (char *)dlerror();
    }
    return addr;
}

int cLibClose(void *hdl)
{
    int ret = dlclose(hdl);
    if (ret != 0)
        return -1;
    return 0;
}

// 加载c库，初始化函数指针
const char *loadWrapperSym(void *hdl, char **loadErr)
{
    // 加载函数名为helloSymbol的地址，并转化为helloFunc类型函数指针
    if ((cPtrHello = cLibLoad(hdl, helloSymbol, loadErr)) == NULL)
        return helloSymbol;
    return NULL;
}

// 接口适配，调用c库
void callHello(char *name)
{
    return cPtrHello(name);
}