package main

import (
	"fmt"
	"log"

	ort "github.com/yalue/onnxruntime_go"
)

func main() {
	// 1. 设置.so路径
	ort.SetSharedLibraryPath("/feng/Projects/RustProjects/tokenizer_wrapper/go_binding/inference/lib/libonnxruntime.so")

	if err := ort.InitializeEnvironment(); err != nil {
		log.Fatalf("InitializeEnvironment failed: %v", err)
	}
	defer ort.DestroyEnvironment()

	// 2. 输入tensor
	ids := []int64{0, 6, 3515, 635, 17737, 2272, 19651, 2, 2, 6, 19390, 10970, 2283, 2767, 4695, 2}
	typeIds := []int64{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0}

	inputShape := ort.NewShape(1, 16)
	outputShape := ort.NewShape(1, 2)

	idsTensor, err := ort.NewTensor(inputShape, ids)
	if err != nil {
		log.Fatalf("NewTensor failed: %v", err)
	}
	defer idsTensor.Destroy()

	attentionMaskTensor, err := ort.NewTensor(inputShape, typeIds)
	if err != nil {
		log.Fatalf("NewTensor failed: %v", err)
	}
	defer attentionMaskTensor.Destroy()

	outputTensor, err := ort.NewEmptyTensor[float32](outputShape)
	if err != nil {
		log.Fatalf("NewEmptyTensor failed: %v", err)
	}
	defer outputTensor.Destroy()

	outputTensor2, err := ort.NewEmptyTensor[float32](outputShape)
	if err != nil {
		log.Fatalf("NewEmptyTensor failed: %v", err)
	}
	defer outputTensor2.Destroy()

	option, err := ort.NewSessionOptions()
	if err != nil {
		log.Fatalf("NewSessionOptions failed: %v", err)
	}
	defer option.Destroy()

	cudaOption, err := ort.NewCUDAProviderOptions()
	if err != nil {
		log.Fatalf("NewCUDAProviderOptions failed: %v", err)
	}
	defer cudaOption.Destroy()

	cudaOption.Update(map[string]string{"device_id": "1"})

	option.AppendExecutionProviderCUDA(cudaOption)

	session, err := ort.NewDynamicAdvancedSession("/feng/Projects/RustProjects/tokenizer_wrapper/go_binding/inference/onnx/model.onnx", []string{"input_ids", "token_type_ids"}, []string{"output", "562"}, option)
	if err != nil {
		log.Fatalf("NewSession failed: %v", err)
	}
	defer session.Destroy()

	if err := session.Run([]ort.Value{idsTensor, attentionMaskTensor}, []ort.Value{outputTensor, outputTensor2}); err != nil {
		log.Fatalf("Run failed: %v", err)
	}

	fmt.Printf("output: %v\n", outputTensor.GetData())
}
