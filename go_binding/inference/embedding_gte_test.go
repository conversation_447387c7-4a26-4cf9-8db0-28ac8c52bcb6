package inference

import (
	"fmt"
	"image/color"
	"runtime"
	"sync"
	"testing"
	"time"

	wrapper "tokenizer_wrapper/tokenizer_wrapper"

	ort "github.com/yalue/onnxruntime_go"
	"gonum.org/v1/plot"
	"gonum.org/v1/plot/plotter"
	"gonum.org/v1/plot/vg"
)

func TestEmbeddingGte(t *testing.T) {
	// 1. 初始化ort
	deviceId := 1
	///feng/Projects/RustProjects/tokenizer_wrapper/go_binding/inference/onnx/gte-ft-v3.0.onnx
	///feng/Projects/RustProjects/tokenizer_wrapper/go_binding/inference/lib/libonnxruntime.so
	onnxPath := "./onnx/gte-ft-v3.0.onnx"
	libPath := "./lib/libonnxruntime.so"

	inference := NewInference(libPath, onnxPath, deviceId)
	if inference == nil {
		t.Errorf("failed to create inference")
	}
	defer inference.Destroy()

	// 2. 初始化tokenizer
	// modelName := "Xenova/ernie-3.0-nano-zh"
	modelName := "Alibaba-NLP/gte-multilingual-base"
	useLocal, padId, padFixed, maxLength := 0, 0, 0, 512
	tokenizer := wrapper.NewTokenizerWrapper(modelName, useLocal, padId, padFixed, maxLength)

	queries := []string{"中国", "我爱中国", "北京"}

	encodings, _ := tokenizer.EncodeBatch(queries, true)
	ids := make([][]int64, len(encodings))
	masks := make([][]int64, len(encodings))

	for i, encoding := range encodings {
		ids[i] = encoding.Ids
		masks[i] = encoding.Masks
	}

	// 3. 创建tensor
	idsTensor, _ := NewTensorFrom[int64](ids)
	masksTensor, _ := NewTensorFrom[int64](masks)

	fmt.Println(idsTensor.GetShape())
	fmt.Println(masksTensor.GetShape())
	tensorMap := map[string]ort.Value{
		"input_ids":      idsTensor,
		"attention_mask": masksTensor,
	}

	outputMap := map[string][]int64{
		"logits": {int64(len(queries)), 5, 768},
	}

	// 4. 运行
	resMap, err := inference.Infer(tensorMap, outputMap)
	if err != nil {
		t.Errorf("failed to infer: %v", err)
	}

	for outName, tensor := range resMap {
		shape := tensor.GetShape()
		data := tensor.GetData()

		fmt.Printf("outName: %s, shape: %v, data: %v\n", outName, shape, data)
		tensor.Destroy()
	}
}

type DiffLen struct {
	qlen     int64
	avgmicro int64
}

func ImgaesDiffLen(disSlice []DiffLen, disStringLen []DiffLen, name string) {
	// 创建一个新的图表
	p := plot.New()

	// 设置图表标题
	p.Title.Text = "difflen_throughput"
	p.X.Label.Text = "query_len"
	p.Y.Label.Text = "throughput/s"

	// 定义折线的数据 (X, Y)
	lineData1 := plotter.XYs{}

	for _, v := range disSlice {
		lineData1 = append(lineData1, plotter.XY{
			X: float64(v.qlen),
			Y: (float64(1*1000*1000) / float64(v.avgmicro)) * float64(v.qlen),
		})

		fmt.Printf("qlen %d: avg %d microseonds,throuthput: %f\n", v.qlen, v.avgmicro, (float64(1*1000*1000)/float64(v.avgmicro))*float64(v.qlen))
	}

	// 创建一个折线图，并将数据添加到图表中
	line1, err := plotter.NewLine(lineData1)
	if err != nil {
		fmt.Println("Error creating line:", err)
		return
	}

	line1.Color = color.RGBA{G: 255, A: 255} // 设置为绿色
	p.Add(line1)

	// 定义折线的数据 (X, Y)
	lineData2 := plotter.XYs{}

	for _, v := range disStringLen {
		lineData2 = append(lineData2, plotter.XY{
			X: float64(v.qlen),
			Y: (float64(1*1000*1000) / float64(v.avgmicro)) * float64(v.qlen),
		})

		fmt.Printf("qlen %d: avg %d microseonds,throuthput: %f\n", v.qlen, v.avgmicro, (float64(1*1000*1000)/float64(v.avgmicro))*float64(v.qlen))
	}

	// 创建一个折线图，并将数据添加到图表中
	line2, err := plotter.NewLine(lineData2)
	if err != nil {
		fmt.Println("Error creating line:", err)
		return
	}

	line2.Color = color.RGBA{R: 255, A: 255} // 红色
	p.Add(line2)

	// 创建一个 PNG 文件来保存图表
	if err := p.Save(6*vg.Inch, 4*vg.Inch, name); err != nil {
		fmt.Println("Error saving plot:", err)
	}
	fmt.Println("折线图已保存为", name)
}

func ImgaesMaxBatch(disSlice []DiffLen, name string) {
	// 创建一个新的图表
	p := plot.New()

	// 设置图表标题
	p.Title.Text = "maxBatch_throughput"
	p.X.Label.Text = "batchSize"
	p.Y.Label.Text = "throughput/s"

	// 定义折线的数据 (X, Y)
	lineData1 := plotter.XYs{}

	for _, v := range disSlice {
		lineData1 = append(lineData1, plotter.XY{
			X: float64(v.qlen),
			Y: (float64(1*1000*1000) / float64(v.avgmicro)) * float64(v.qlen),
		})

		fmt.Printf("qlen %d: avg %d microseonds,throuthput: %f\n", v.qlen, v.avgmicro, (float64(1*1000*1000)/float64(v.avgmicro))*float64(v.qlen))
	}

	// 创建一个折线图，并将数据添加到图表中
	line1, err := plotter.NewLine(lineData1)
	if err != nil {
		fmt.Println("Error creating line:", err)
		return
	}

	line1.Color = color.RGBA{G: 255, A: 255} // 设置为绿色
	p.Add(line1)

	// 创建一个 PNG 文件来保存图表
	if err := p.Save(6*vg.Inch, 4*vg.Inch, name); err != nil {
		fmt.Println("Error saving plot:", err)
	}
	fmt.Println("折线图已保存为", name)
}

func ImgaesGouroutines(all_diff [][]DiffLen, name string) {
	// 创建一个新的图表
	p := plot.New()

	// 设置图表标题
	p.Title.Text = "maxBatch_throughput"
	p.X.Label.Text = "batchSize"
	p.Y.Label.Text = "throughput/s"

	lineData2 := plotter.XYs{}

	for i := 0; i < len(all_diff[0]); i++ {
		x := all_diff[0][i].qlen
		y := float64(0)

		for _, vs := range all_diff {
			//fmt.Println("multi i:",i,vs[i].qlen,(float64(1*1000*1000)/float64(vs[i].avgmicro))*float64(vs[i].qlen))
			y += (float64(1*1000*1000) / float64(vs[i].avgmicro)) * float64(vs[i].qlen)
		}

		fmt.Println("multi i:", i, all_diff[0][i].qlen, y)
		lineData2 = append(lineData2, plotter.XY{
			X: float64(x),
			Y: float64(y),
		})
	}

	// 创建一个折线图，并将数据添加到图表中
	line2, err := plotter.NewLine(lineData2)
	if err != nil {
		fmt.Println("Error creating line:", err)
		return
	}

	line2.Color = color.RGBA{R: 255, A: 255} // 红色
	p.Add(line2)

	// 创建一个 PNG 文件来保存图表
	if err := p.Save(6*vg.Inch, 4*vg.Inch, name); err != nil {
		fmt.Println("Error saving plot:", err)
	}
	fmt.Println("折线图已保存为", name)
}

func calculateMean(arr []int64) int64 {
	// 计算数组元素的总和
	sum := int64(0)
	for _, value := range arr {
		sum += value
	}

	// 计算平均值
	return sum / int64(len(arr))
}

const NUM = 100
const INS_NUM = 2

// 单实例，单处理线程
func TestEmbeddingGte_0(t *testing.T) {
	// 1. 初始化ort
	deviceId := 0
	onnxPath := "./onnx/gte-ft-v3.0.onnx"
	libPath := "./lib/libonnxruntime.so"

	inference := NewInference(libPath, onnxPath, deviceId)
	if inference == nil {
		t.Errorf("failed to create inference")
	}
	defer inference.Destroy()

	// 2. 初始化tokenizer
	// modelName := "Xenova/ernie-3.0-nano-zh"
	modelName := "Alibaba-NLP/gte-multilingual-base"
	useLocal, padId, padFixed, maxLength := 0, 0, 0, 512
	tokenizer := wrapper.NewTokenizerWrapper(modelName, useLocal, padId, padFixed, maxLength)

	//前,后10次结果不要
	predisCard := 10
	postDiscard := 10
	// 4. 运行
	elpases_all := make([]int64, 0, NUM)
	for i := 0; i < NUM; i++ {
		//, "我爱中国", "北京"
		queries := []string{"二", "二"}

		encodings, _ := tokenizer.EncodeBatch(queries, true)
		ids := make([][]int64, len(encodings))
		masks := make([][]int64, len(encodings))

		for i, encoding := range encodings {
			ids[i] = encoding.Ids
			masks[i] = encoding.Masks
		}

		// 3. 创建tensor
		idsTensor, _ := NewTensorFrom[int64](ids)
		masksTensor, _ := NewTensorFrom[int64](masks)

		tensorMap := map[string]ort.Value{
			"input_ids":      idsTensor,
			"attention_mask": masksTensor,
		}
		outputMap := map[string][]int64{
			"logits": {int64(len(queries)), 5, 768},
		}

		now := time.Now()
		resMap, err := inference.Infer(tensorMap, outputMap)
		if err != nil {
			t.Errorf("failed to infer: %v", err)
		}

		if i > predisCard && i < (NUM-postDiscard) {
			elpase := time.Now().Sub(now).Microseconds()
			elpases_all = append(elpases_all, elpase)
		}

		for outName, tensor := range resMap {
			shape := tensor.GetShape()
			data := tensor.GetData()

			fmt.Printf("outName: %s, shape: %v, data: %v\n", outName, shape, data[:4])
			tensor.Destroy()
		}

	}

	fmt.Printf("time: avg %d microseonds\n", calculateMean(elpases_all))
}

// 测试不同样本长度的影响
func TestEmbeddingGte_11(t *testing.T) {
	// 1. 初始化ort
	deviceId := 0
	onnxPath := "./onnx/gte-ft-v3.0.onnx"
	libPath := "./lib/libonnxruntime.so"
	const NUM = 100

	inference := NewInference(libPath, onnxPath, deviceId)
	if inference == nil {
		t.Errorf("failed to create inference")
	}
	defer inference.Destroy()

	// 2. 初始化tokenizer
	// modelName := "Xenova/ernie-3.0-nano-zh"
	modelName := "Alibaba-NLP/gte-multilingual-base"
	useLocal, padId, padFixed, maxLength := 0, 0, 0, 512
	tokenizer := wrapper.NewTokenizerWrapper(modelName, useLocal, padId, padFixed, maxLength)

	//前,后10次结果不要
	predisCard := 10
	postDiscard := 10
	// 4. 运行

	charLen := 50

	diffLens := make([]DiffLen, 0, charLen*NUM)

	for k := 1; k < charLen; k++ {
		elpases_all := make([]int64, 0, NUM)
		for i := 0; i < NUM; i++ {
			//, "我爱中国", "北京"
			queries := []string{}
			for kk := 0; kk < k; kk++ {
				queries = append(queries, "二")
			}

			encodings, _ := tokenizer.EncodeBatch(queries, true)
			ids := make([][]int64, len(encodings))
			masks := make([][]int64, len(encodings))

			for i, encoding := range encodings {
				ids[i] = encoding.Ids
				masks[i] = encoding.Masks
			}

			// 3. 创建tensor
			idsTensor, _ := NewTensorFrom[int64](ids)
			masksTensor, _ := NewTensorFrom[int64](masks)

			tensorMap := map[string]ort.Value{
				"input_ids":      idsTensor,
				"attention_mask": masksTensor,
			}
			outputMap := map[string][]int64{
				"logits": {int64(len(queries)), 3, 768},
			}

			now := time.Now()
			resMap, err := inference.Infer(tensorMap, outputMap)
			if err != nil {
				t.Errorf("failed to infer: %v", err)
			}

			if i > predisCard && i < (NUM-postDiscard) {
				elpase := time.Now().Sub(now).Microseconds()
				elpases_all = append(elpases_all, elpase)
			}

			for _, tensor := range resMap {
				//shape := tensor.GetShape()
				//data := tensor.GetData()

				//fmt.Printf("outName: %s, shape: %v, data: %v\n", outName, shape, data[:4])
				tensor.Destroy()
			}

		}

		diffLens = append(diffLens, DiffLen{
			qlen:     int64(k),
			avgmicro: calculateMean(elpases_all),
		})

		runtime.GC()
	}

	difStringfLens := make([]DiffLen, 0, charLen*NUM)

	for k := 1; k < charLen; k++ {
		elpases_all := make([]int64, 0, NUM)
		for i := 0; i < NUM; i++ {
			//, "我爱中国", "北京"
			queries := []string{}
			var input = ""
			for kk := 0; kk < k; kk++ {
				input = input + "二"
			}

			queries = append(queries, input)

			encodings, _ := tokenizer.EncodeBatch(queries, true)
			ids := make([][]int64, len(encodings))
			masks := make([][]int64, len(encodings))

			for i, encoding := range encodings {
				ids[i] = encoding.Ids
				masks[i] = encoding.Masks
			}

			// 3. 创建tensor
			idsTensor, _ := NewTensorFrom[int64](ids)
			masksTensor, _ := NewTensorFrom[int64](masks)

			tensorMap := map[string]ort.Value{
				"input_ids":      idsTensor,
				"attention_mask": masksTensor,
			}
			outputMap := map[string][]int64{
				"logits": {int64(len(queries)), idsTensor.GetShape()[1], 768},
			}

			now := time.Now()
			resMap, err := inference.Infer(tensorMap, outputMap)
			if err != nil {
				t.Errorf("failed to infer: %v", err)
			}

			if i > predisCard && i < (NUM-postDiscard) {
				elpase := time.Now().Sub(now).Microseconds()
				elpases_all = append(elpases_all, elpase)
			}

			for _, tensor := range resMap {
				//shape := tensor.GetShape()
				//data := tensor.GetData()

				//fmt.Printf("outName: %s, shape: %v, data: %v\n", outName, shape, data[:4])
				tensor.Destroy()
			}

		}

		difStringfLens = append(difStringfLens, DiffLen{
			qlen:     int64(k),
			avgmicro: calculateMean(elpases_all),
		})

		runtime.GC()
	}

	ImgaesDiffLen(diffLens, difStringfLens, "diff_len.png")
}

// 单实例最佳batchsize
func TestEmbeddingGte_12(t *testing.T) {
	// 1. 初始化ort
	deviceId := 0
	onnxPath := "./onnx/gte-ft-v3.0.onnx"
	libPath := "./lib/libonnxruntime.so"
	const NUM = 100

	inference := NewInference(libPath, onnxPath, deviceId)
	if inference == nil {
		t.Errorf("failed to create inference")
	}
	defer inference.Destroy()

	// 2. 初始化tokenizer
	// modelName := "Xenova/ernie-3.0-nano-zh"
	modelName := "Alibaba-NLP/gte-multilingual-base"
	useLocal, padId, padFixed, maxLength := 0, 0, 0, 512
	tokenizer := wrapper.NewTokenizerWrapper(modelName, useLocal, padId, padFixed, maxLength)

	//前,后10次结果不要
	predisCard := 10
	postDiscard := 10
	// 4. 运行

	charLen := 1000

	diffLens := make([]DiffLen, 0, charLen*NUM)

	for k := 1; k < charLen; k++ {

		if k%50 != 0 {
			continue
		}

		elpases_all := make([]int64, 0, NUM)
		for i := 0; i < NUM; i++ {
			//, "我爱中国", "北京"
			queries := []string{}
			for kk := 0; kk < k; kk++ {
				queries = append(queries, "二")
			}

			encodings, _ := tokenizer.EncodeBatch(queries, true)
			ids := make([][]int64, len(encodings))
			masks := make([][]int64, len(encodings))

			for i, encoding := range encodings {
				ids[i] = encoding.Ids
				masks[i] = encoding.Masks
			}

			// 3. 创建tensor
			idsTensor, _ := NewTensorFrom[int64](ids)
			masksTensor, _ := NewTensorFrom[int64](masks)

			tensorMap := map[string]ort.Value{
				"input_ids":      idsTensor,
				"attention_mask": masksTensor,
			}
			outputMap := map[string][]int64{
				"logits": {int64(len(queries)), 3, 768},
			}

			now := time.Now()
			resMap, err := inference.Infer(tensorMap, outputMap)
			if err != nil {
				t.Errorf("failed to infer: %v", err)
			}

			if i > predisCard && i < (NUM-postDiscard) {
				elpase := time.Now().Sub(now).Microseconds()
				elpases_all = append(elpases_all, elpase)
			}

			for _, tensor := range resMap {
				//shape := tensor.GetShape()
				//data := tensor.GetData()

				//fmt.Printf("outName: %s, shape: %v, data: %v\n", outName, shape, data[:4])
				tensor.Destroy()
			}

		}

		diffLens = append(diffLens, DiffLen{
			qlen:     int64(k),
			avgmicro: calculateMean(elpases_all),
		})

		runtime.GC()
	}

	ImgaesMaxBatch(diffLens, "maxbatch.png")
}

type Input struct {
	tensorin *map[string]ort.Value
	output   *map[string][]int64
}

// 单实例多线程：数据向量化提前预处理，仅执行GpuInfer。
func TestEmbeddingGte_14(t *testing.T) {
	// 1. 初始化ort
	deviceId := 0
	onnxPath := "./onnx/gte-ft-v3.0.onnx"
	libPath := "./lib/libonnxruntime.so"
	const NUM = 1000

	inference := NewInference(libPath, onnxPath, deviceId)
	if inference == nil {
		t.Errorf("failed to create inference")
	}
	defer inference.Destroy()

	// 2. 初始化tokenizer
	// modelName := "Xenova/ernie-3.0-nano-zh"
	modelName := "Alibaba-NLP/gte-multilingual-base"
	useLocal, padId, padFixed, maxLength := 0, 0, 0, 512
	tokenizer := wrapper.NewTokenizerWrapper(modelName, useLocal, padId, padFixed, maxLength)

	//前,后10次结果不要
	predisCard := 10
	postDiscard := 10
	// 4. 运行

	charLen := 450

	wg := &sync.WaitGroup{}
	gn := 16

	inputss := make([][]Input, 0, gn)

	queries := []string{}
	for kk := 0; kk < charLen; kk++ {
		queries = append(queries, "二二二二二二二二二二二")
	}



	
	for i := 0; i < gn; i++ {
		inputs := make([]Input, 0, charLen*NUM)
		for j := 0; j < NUM; j++ {
			//, "我爱中国", "北京"
			encodings, _ := tokenizer.EncodeBatch(queries, true)
			ids := make([][]int64, len(encodings))
			masks := make([][]int64, len(encodings))

			for l, encoding := range encodings {
				ids[l] = encoding.Ids
				masks[l] = encoding.Masks
			}

			// 3. 创建tensor
			idsTensor, _ := NewTensorFrom[int64](ids)
			masksTensor, _ := NewTensorFrom[int64](masks)

			tensorMap := map[string]ort.Value{
				"input_ids":      idsTensor,
				"attention_mask": masksTensor,
			}
			outputMap := map[string][]int64{
				"logits": {int64(len(queries)),idsTensor.GetShape()[1], 768},
			}

			inputs = append(inputs, Input{
				tensorin: &tensorMap,
				output:   &outputMap,
			})
		}

		inputss = append(inputss, inputs)
	}

	for i := 0; i < gn; i++ {
		index := i
		wg.Add(1)
		go func() {
			defer wg.Done()
			inputs := inputss[index]
			elpases_all := make([]int64, 0, NUM)
			fmt.Println("start:", len(inputs))
			for k, _ := range inputs {
				now := time.Now()
				_, err := inference.Infer(*inputs[k].tensorin, *inputs[k].output)
				if err != nil {
					t.Errorf("failed to infer: %v", err)
				}

				if k > predisCard && k < (NUM-postDiscard) {
					elpase := time.Now().Sub(now).Microseconds()
					elpases_all = append(elpases_all, elpase)
				}
			}

			avg := calculateMean(elpases_all)
			fmt.Println("elpase avg:", avg, 1*1000*1000/avg)
		}()
	}

	wg.Wait()
}


//测试单个batch和多个batch，在相同总字符长度下的影响
func TestEmbeddingGte_16(t *testing.T) {
	// 1. 初始化ort
	deviceId := 0
	onnxPath := "./onnx/gte-ft-v3.0.onnx"
	libPath := "./lib/libonnxruntime.so"
	const NUM = 1000

	inference := NewInference(libPath, onnxPath, deviceId)
	if inference == nil {
		t.Errorf("failed to create inference")
	}
	defer inference.Destroy()

	// 2. 初始化tokenizer
	// modelName := "Xenova/ernie-3.0-nano-zh"
	modelName := "Alibaba-NLP/gte-multilingual-base"
	useLocal, padId, padFixed, maxLength := 0, 0, 0, 512
	tokenizer := wrapper.NewTokenizerWrapper(modelName, useLocal, padId, padFixed, maxLength)

	//前,后10次结果不要
	predisCard := 10
	postDiscard := 10
	// 4. 运行

	charLen := 1
	wg := &sync.WaitGroup{}
	gn := 1

	inputss := make([][]Input, 0, gn)

	queries := []string{}
	
	for kk := 0; kk < charLen; kk++ {
		queries = append(queries, "二")
	}
		
	
	/*

	var query = ""
	for kk := 0; kk < charLen; kk++ {
		query += "二"
	}

	queries = append(queries, query)
	*/

	for i := 0; i < gn; i++ {
		inputs := make([]Input, 0, charLen*NUM)
		for j := 0; j < NUM; j++ {
			//, "我爱中国", "北京"


			encodings, _ := tokenizer.EncodeBatch(queries, true)
			ids := make([][]int64, len(encodings))
			masks := make([][]int64, len(encodings))

			for l, encoding := range encodings {
				ids[l] = encoding.Ids
				masks[l] = encoding.Masks
			}

			// 3. 创建tensor
			idsTensor, _ := NewTensorFrom[int64](ids)
			masksTensor, _ := NewTensorFrom[int64](masks)

			tensorMap := map[string]ort.Value{
				"input_ids":      idsTensor,
				"attention_mask": masksTensor,
			}
			outputMap := map[string][]int64{
				"logits": {int64(len(queries)),idsTensor.GetShape()[1], 768},
			}

			inputs = append(inputs, Input{
				tensorin: &tensorMap,
				output:   &outputMap,
			})
		}

		inputss = append(inputss, inputs)
	}

	for i := 0; i < gn; i++ {
		index := i
		wg.Add(1)
		go func() {
			defer wg.Done()
			inputs := inputss[index]
			elpases_all := make([]int64, 0, NUM)
			fmt.Println("start:", len(inputs))
			for k, _ := range inputs {
				now := time.Now()
				_, err := inference.Infer(*inputs[k].tensorin, *inputs[k].output)
				if err != nil {
					t.Errorf("failed to infer: %v", err)
				}

				if k > predisCard && k < (NUM-postDiscard) {
					elpase := time.Now().Sub(now).Microseconds()
					elpases_all = append(elpases_all, elpase)
				}
			}

			avg := calculateMean(elpases_all)
			fmt.Println("elpase avg:", avg, 1*1000*1000/avg)
		}()
	}

	wg.Wait()
}

func TestEmbeddingGte_15(t *testing.T) {
	// 1. 初始化ort
	deviceId := 0
	onnxPath := "./onnx/gte-ft-v3.0.onnx"
	libPath := "./lib/libonnxruntime.so"
	const NUM = 1000



	// 2. 初始化tokenizer
	// modelName := "Xenova/ernie-3.0-nano-zh"
	modelName := "Alibaba-NLP/gte-multilingual-base"
	useLocal, padId, padFixed, maxLength := 0, 0, 0, 512
	tokenizer := wrapper.NewTokenizerWrapper(modelName, useLocal, padId, padFixed, maxLength)

	//前,后10次结果不要
	predisCard := 10
	postDiscard := 10
	// 4. 运行

	charLen := 128

	gn := 8

	infers := make([]*Inference, 0, gn)

	wg := &sync.WaitGroup{}

	inputss := make([][]Input, 0, gn)

	queries := []string{}
	for kk := 0; kk < charLen; kk++ {
		queries = append(queries, "二")
	}

	for i := 0; i < gn; i++ {
		inference := NewInference(libPath, onnxPath, deviceId)
		if inference == nil {
			t.Errorf("failed to create inference")
		}
		defer inference.Destroy()
		infers = append(infers, inference)

		inputs := make([]Input, 0, charLen*NUM)
		for j := 0; j < NUM; j++ {
			//, "我爱中国", "北京"
			encodings, _ := tokenizer.EncodeBatch(queries, true)
			ids := make([][]int64, len(encodings))
			masks := make([][]int64, len(encodings))

			for l, encoding := range encodings {
				ids[l] = encoding.Ids
				masks[l] = encoding.Masks
			}

			// 3. 创建tensor
			idsTensor, _ := NewTensorFrom[int64](ids)
			masksTensor, _ := NewTensorFrom[int64](masks)

			tensorMap := map[string]ort.Value{
				"input_ids":      idsTensor,
				"attention_mask": masksTensor,
			}
			outputMap := map[string][]int64{
				"logits": {int64(len(queries)), 3, 768},
			}

			inputs = append(inputs, Input{
				tensorin: &tensorMap,
				output:   &outputMap,
			})
		}

		inputss = append(inputss, inputs)
	}

	for i := 0; i < gn; i++ {
		index := i
		wg.Add(1)
		go func() {
			defer wg.Done()
			inputs := inputss[index]
			elpases_all := make([]int64, 0, NUM)
			fmt.Println("start:", len(inputs))
			for k, _ := range inputs {
				now := time.Now()
				_, err := infers[index].Infer(*inputs[k].tensorin, *inputs[k].output)
				if err != nil {
					t.Errorf("failed to infer: %v", err)
				}

				if k > predisCard && k < (NUM-postDiscard) {
					elpase := time.Now().Sub(now).Microseconds()
					elpases_all = append(elpases_all, elpase)
				}
			}

			avg := calculateMean(elpases_all)
			fmt.Println("elpase avg:", avg, 1*1000*1000/avg)
		}()
	}

	wg.Wait()
}

// 单实例多线程,数据预处理+GPUInfer
func TestEmbeddingGte_13(t *testing.T) {
	// 1. 初始化ort
	deviceId := 0
	onnxPath := "./onnx/gte-ft-v3.0.onnx"
	libPath := "./lib/libonnxruntime.so"
	const NUM = 100

	inference := NewInference(libPath, onnxPath, deviceId)
	if inference == nil {
		t.Errorf("failed to create inference")
	}
	defer inference.Destroy()

	// 2. 初始化tokenizer
	// modelName := "Xenova/ernie-3.0-nano-zh"
	modelName := "Alibaba-NLP/gte-multilingual-base"
	useLocal, padId, padFixed, maxLength := 0, 0, 0, 512
	tokenizer := wrapper.NewTokenizerWrapper(modelName, useLocal, padId, padFixed, maxLength)

	//前,后10次结果不要
	predisCard := 10
	postDiscard := 10
	// 4. 运行

	charLen := 500
	gn := 4
	var lock sync.Mutex

	all_diff := make([][]int64, 0, gn)
	wg := &sync.WaitGroup{}
	for g := 0; g < gn; g++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			elpases_all := make([]int64, 0, NUM)

			//, "我爱中国", "北京"
			queries := []string{}
			for kk := 0; kk < charLen; kk++ {
				queries = append(queries, "二")
			}

			for i := 0; i < NUM; i++ {
				encodings, _ := tokenizer.EncodeBatch(queries, true)
				ids := make([][]int64, len(encodings))
				masks := make([][]int64, len(encodings))

				for i, encoding := range encodings {
					ids[i] = encoding.Ids
					masks[i] = encoding.Masks
				}

				// 3. 创建tensor
				idsTensor, _ := NewTensorFrom[int64](ids)
				masksTensor, _ := NewTensorFrom[int64](masks)

				tensorMap := map[string]ort.Value{
					"input_ids":      idsTensor,
					"attention_mask": masksTensor,
				}
				outputMap := map[string][]int64{
					"logits": {int64(len(queries)), 3, 768},
				}

				now := time.Now()
				resMap, err := inference.Infer(tensorMap, outputMap)
				if err != nil {
					t.Errorf("failed to infer: %v", err)
				}

				if i > predisCard && i < (NUM-postDiscard) {
					elpase := time.Now().Sub(now).Microseconds()
					elpases_all = append(elpases_all, elpase)
				}

				for _, tensor := range resMap {
					//shape := tensor.GetShape()
					//data := tensor.GetData()

					//fmt.Printf("outName: %s, shape: %v, data: %v\n", outName, shape, data[:4])
					tensor.Destroy()
				}

			}

			lock.Lock()
			all_diff = append(all_diff, elpases_all)
			lock.Unlock()
		}()
	}

	wg.Wait()

	throughput := int64(0)
	for i := 0; i < gn; i++ {
		avg := calculateMean(all_diff[i])
		fmt.Println("elpase avg:", avg, 1*1000*1000/avg)

		throughput += 1 * 1000 * 1000 / avg
	}

	fmt.Println("all throughput", throughput)
}

// 多实例，多处理线程
func TestEmbeddingGte_1(t *testing.T) {
	// 1. 初始化ort
	deviceId := 0
	onnxPath := "./onnx/gte-ft-v3.0.onnx"
	libPath := "./lib/libonnxruntime.so"

	queries := []string{"中国", "我爱中国", "北京"}
	// 4. 运行

	infers := make([]*Inference, INS_NUM)
	for i := 0; i < INS_NUM; i++ {
		infers[i] = NewInference(libPath, onnxPath, deviceId)
		defer infers[i].Destroy()

	}

	// 2. 初始化tokenizer
	// modelName := "Xenova/ernie-3.0-nano-zh"
	modelName := "Alibaba-NLP/gte-multilingual-base"
	useLocal, padId, padFixed, maxLength := 0, 0, 0, 512

	tokens := make([]*wrapper.TokenizerWrapper, INS_NUM)

	for i := 0; i < INS_NUM; i++ {
		tokens[i] = wrapper.NewTokenizerWrapper(modelName, useLocal, padId, padFixed, maxLength)
	}

	now := time.Now()
	wg := sync.WaitGroup{}
	for i := 0; i < INS_NUM; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < NUM; j++ {
				encodings, _ := tokens[i].EncodeBatch(queries, true)
				ids := make([][]int64, len(encodings))
				masks := make([][]int64, len(encodings))

				for k, encoding := range encodings {
					ids[k] = encoding.Ids
					masks[k] = encoding.Masks
				}

				// 3. 创建tensor
				idsTensor, _ := NewTensorFrom[int64](ids)
				masksTensor, _ := NewTensorFrom[int64](masks)

				tensorMap := map[string]ort.Value{
					"input_ids":      idsTensor,
					"attention_mask": masksTensor,
				}
				outputMap := map[string][]int64{
					"logits": {int64(len(queries)), 5, 768},
				}
				resMap, err := infers[i].Infer(tensorMap, outputMap)
				if err != nil {
					t.Errorf("failed to infer: %v", err)
				}

				for outName, tensor := range resMap {
					shape := tensor.GetShape()
					data := tensor.GetData()

					fmt.Printf("outName: %s, shape: %v, data: %v\n", outName, shape, data[:4])
					tensor.Destroy()
				}
			}

		}()
	}
	wg.Wait()

	fmt.Printf("time: %vms\n", time.Since(now).Milliseconds())
}

// 单实例，多处理线程
func TestEmbeddingGte_2(t *testing.T) {
	// 1. 初始化ort
	deviceId := 0
	onnxPath := "./onnx/gte-ft-v3.0.onnx"
	libPath := "./lib/libonnxruntime.so"

	inference := NewInference(libPath, onnxPath, deviceId)
	if inference == nil {
		t.Errorf("failed to create inference")
	}
	defer inference.Destroy()

	// 2. 初始化tokenizer
	// modelName := "Xenova/ernie-3.0-nano-zh"
	modelName := "Alibaba-NLP/gte-multilingual-base"
	useLocal, padId, padFixed, maxLength := 0, 0, 0, 512
	tokenizer := wrapper.NewTokenizerWrapper(modelName, useLocal, padId, padFixed, maxLength)

	queries := []string{"中国", "我爱中国", "北京"}
	// 4. 运行
	now := time.Now()
	wg := sync.WaitGroup{}
	for i := 0; i < INS_NUM; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for i := 0; i < NUM; i++ {
				encodings, _ := tokenizer.EncodeBatch(queries, true)
				ids := make([][]int64, len(encodings))
				masks := make([][]int64, len(encodings))

				for i, encoding := range encodings {
					ids[i] = encoding.Ids
					masks[i] = encoding.Masks
				}

				// 3. 创建tensor
				idsTensor, _ := NewTensorFrom[int64](ids)
				masksTensor, _ := NewTensorFrom[int64](masks)

				tensorMap := map[string]ort.Value{
					"input_ids":      idsTensor,
					"attention_mask": masksTensor,
				}
				outputMap := map[string][]int64{
					"logits": {int64(len(queries)), 5, 768},
				}
				resMap, err := inference.Infer(tensorMap, outputMap)
				if err != nil {
					t.Errorf("failed to infer: %v", err)
				}

				for outName, tensor := range resMap {
					shape := tensor.GetShape()
					data := tensor.GetData()

					fmt.Printf("outName: %s, shape: %v, data: %v\n", outName, shape, data[:4])
					tensor.Destroy()
				}
			}
		}()
	}
	wg.Wait()

	fmt.Printf("time: %vms\n", time.Since(now).Milliseconds())
}
