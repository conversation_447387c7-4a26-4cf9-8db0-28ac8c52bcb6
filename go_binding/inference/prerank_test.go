package inference

import (
	"fmt"
	"testing"
	wrapper "tokenizer_wrapper/tokenizer_wrapper"

	"git.iflytek.com/RS_DataHub/ifly_search_common/onnx"
	ort "github.com/yalue/onnxruntime_go"
)

func TestPrerank(t *testing.T) {
	// 1. 初始化ort
	deviceId := 1
	onnxPath := "./onnx/model_fp16.onnx"
	libPath := "./lib/libonnxruntime.so"

	inference := NewInference(libPath, onnxPath, deviceId)
	if inference == nil {
		t.Errorf("failed to create inference")
	}
	defer inference.Destroy()

	// 2. 初始化tokenizer
	// modelName := "Xenova/ernie-3.0-nano-zh"
	modelName := "/feng/IflySearch_Resource/Prerank/1212/Xenova-ernie-3.0-nano-zh/tokenizer.json"
	useLocal, padId, padFixed, maxLength := 1, 0, 0, 512
	tokenizer := wrapper.NewTokenizerWrapper(modelName, useLocal, padId, padFixed, maxLength)

	queries := []string{"query1", "query2", "query3"}
	answers := []string{"answer1", "answer2", "answer3"}
	batchSize := len(queries)
	encodings, err := tokenizer.EncodeBatchPair(queries, answers, true)
	if err != nil {
		t.Logf("failed to tokenize: %v", err)
	}
	ids := make([][]int64, batchSize)
	types := make([][]int64, batchSize)
	masks := make([][]int64, batchSize)

	for i, encoding := range encodings {
		ids[i] = encoding.Ids
		types[i] = encoding.Types
		masks[i] = encoding.Masks
	}

	// 3. 创建tensor
	idsTensor, _ := onnx.NewTensorFrom[int64](ids)
	typeTensor, _ := onnx.NewTensorFrom[int64](types)
	masksTensor, _ := onnx.NewTensorFrom[int64](masks)
	defer idsTensor.Destroy()
	defer masksTensor.Destroy()

	tensorMap := map[string]ort.Value{
		"input_ids":      idsTensor,
		"attention_mask": masksTensor,
		"token_type_ids": typeTensor,
	}

	outputMap := map[string][]int64{
		"output": {int64(batchSize), 1},
	}

	// 4. 运行
	resMap, err := inference.Infer(tensorMap, outputMap)
	if err != nil {
		t.Logf("failed to infer: %v", err)
	}

	logitsTensor := resMap["output"]
	defer logitsTensor.Destroy()

	logits := logitsTensor.GetData()

	// 5. 解析
	results := make([][]float32, batchSize)
	for i := 0; i < batchSize; i++ {
		results[i] = make([]float32, 1)
		copy(results[i], logits[i:i+1])
	}
	fmt.Printf("results: %v\n", results)
}
