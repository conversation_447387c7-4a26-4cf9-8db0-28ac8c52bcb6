package inference

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"sync"

	ort "github.com/yalue/onnxruntime_go"
)

var once sync.Once

type Inference struct {
	inputNames  []string
	outputNames []string

	options     *ort.SessionOptions
	cudaOptions *ort.CUDAProviderOptions
	OrtSession  *ort.DynamicAdvancedSession
}

// 创建infer session, deviceId小于0时为cpu
func NewInference(libPath, onnxPath string, deviceId int) *Inference {
	// 1. 设置.so路径
	once.Do(func() {
		ort.SetSharedLibraryPath(libPath)
		if err := ort.InitializeEnvironment(); err != nil {
			log.Fatalf("InitializeEnvironment failed: %v", err)
		}
	})

	instance := &Inference{}

	// 2. 获取输入输出名称
	input, output, err := ort.GetInputOutputInfo(onnxPath)
	if err != nil {
		log.Fatalf("GetInputOutputInfo failed: %v", err)
	}
	inputNames := make([]string, len(input))
	outputNames := make([]string, len(output))

	for i, info := range input {
		inputNames[i] = info.Name
		fmt.Println(info.Name, info.Dimensions)
	}
	for i, info := range output {
		outputNames[i] = info.Name
	}

	// 3. 初始化options
	options, err := ort.NewSessionOptions()
	if err != nil {
		log.Fatalf("NewSessionOptions failed: %v", err)
	}

	if deviceId >= 0 {
		os.Setenv("CUDA_VISIBLE_DEVICES", strconv.Itoa(deviceId))
		cudaOptions, err := ort.NewCUDAProviderOptions()
		if err != nil {
			log.Fatalf("NewCUDAProviderOptions failed: %v", err)
		}
		cudaOptions.Update(map[string]string{"device_id": strconv.Itoa(deviceId)})
		options.AppendExecutionProviderCUDA(cudaOptions)
		instance.cudaOptions = cudaOptions
	}

	// 4. 初始化session
	session, err := ort.NewDynamicAdvancedSession(onnxPath, inputNames, outputNames, options)
	if err != nil {
		log.Fatalf("NewSession failed: %v", err)
	}

	instance.options = options
	instance.OrtSession = session
	instance.inputNames = inputNames
	instance.outputNames = outputNames
	return instance
}

func (i *Inference) Destroy() {
	i.options.Destroy()
	if i.cudaOptions != nil {
		i.cudaOptions.Destroy()
	}
	ort.DestroyEnvironment()
	i.OrtSession.Destroy()
}

func (i *Inference) Infer(inputMap map[string]ort.Value, outputShape map[string][]int64) (map[string]*ort.Tensor[float32], error) {
	// 1. 创建tensor
	input := make([]ort.Value, len(i.inputNames))
	output := make([]ort.Value, len(i.outputNames))

	for index, name := range i.inputNames {
		inputTensor := inputMap[name]
		input[index] = inputTensor
		defer inputTensor.Destroy()
	}

	resMap := make(map[string]*ort.Tensor[float32])

	for index, name := range i.outputNames {
		tensor, err := ort.NewEmptyTensor[float32](ort.NewShape(outputShape[name]...))
		if err != nil {
			return nil, fmt.Errorf("NewEmptyTensor failed: %v", err)
		}
		output[index] = tensor
		resMap[name] = tensor
	}

	if err := i.OrtSession.Run(input, output); err != nil {
		return nil, fmt.Errorf("Infer failed: %v", err)
	}
	return resMap, nil
}

// 由切片创建tensor
func NewTensorFrom[T TensorData](data interface{}) (ort.Value, error) {
	sliceData, shape := Flatten[T](data)
	return ort.NewTensor(ort.NewShape(shape...), sliceData)
}
