package inference

import "reflect"

type FloatData interface {
	~float32 | ~float64
}

type IntData interface {
	~int8 | ~uint8 | ~int16 | ~uint16 | ~int32 | ~uint32 | ~int64 | ~uint64
}

// This is used as a type constraint for the generic Tensor type.
type TensorData interface {
	FloatData | IntData
}

// 获取切片的尺寸
func getDimensions(slice interface{}) []int64 {
	var dimensions []int64
	v := reflect.ValueOf(slice)
	for v.Kind() == reflect.Slice {
		dimensions = append(dimensions, int64(v.Len()))
		if v.Len() > 0 {
			v = v.Index(0)
		} else {
			break
		}
	}
	return dimensions
}

// 递归展平函数，使用泛型
func flatten[T TensorData](slice interface{}, flatSlice *[]T) {
	v := reflect.ValueOf(slice)
	switch v.Kind() {
	case reflect.Slice:
		for i := 0; i < v.Len(); i++ {
			flatten(v.Index(i).Interface(), flatSlice)
		}
	default:
		*flatSlice = append(*flatSlice, slice.(T))
	}
}

// 展平函数，返回一维切片和维度信息
func Flatten[T TensorData](slice interface{}) ([]T, []int64) {
	flatSlice := make([]T, 0)
	flatten(slice, &flatSlice)
	dimensions := getDimensions(slice)
	return flatSlice, dimensions
}
