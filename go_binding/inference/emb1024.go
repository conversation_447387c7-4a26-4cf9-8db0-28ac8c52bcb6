package inference

import (
	"fmt"
	"testing"

	wrapper "tokenizer_wrapper/tokenizer_wrapper"

	ort "github.com/yalue/onnxruntime_go"
)

func TestEmbedding1024(t *testing.T) {
	// 1. 初始化ort
	deviceId := 1
	onnxPath := "./onnx/embedding-gte.onnx"
	libPath := "./lib/libonnxruntime.so"

	inference := NewInference(libPath, onnxPath, deviceId)
	if inference == nil {
		t.<PERSON>("failed to create inference")
	}
	defer inference.Destroy()

	// 2. 初始化tokenizer
	// modelName := "Xenova/ernie-3.0-nano-zh"
	modelName := "Alibaba-NLP/gte-multilingual-base"
	useLocal, padId, padFixed, maxLength := 0, 0, 0, 512
	tokenizer := wrapper.NewTokenizerWrapper(modelName, useLocal, padId, padFixed, maxLength)

	queries := []string{"中国", "我爱中国", "北京"}

	encodings, _ := tokenizer.EncodeBatch(queries, true)
	ids := make([][]int64, len(encodings))
	masks := make([][]int64, len(encodings))

	for i, encoding := range encodings {
		ids[i] = encoding.Ids
		masks[i] = encoding.Masks
	}

	// 3. 创建tensor
	idsTensor, _ := NewTensorFrom[int64](ids)
	masksTensor, _ := NewTensorFrom[int64](masks)

	tensorMap := map[string]ort.Value{
		"input_ids":      idsTensor,
		"attention_mask": masksTensor,
	}

	outputMap := map[string][]int64{
		"logits": {int64(len(queries)), 5, 768},
	}

	// 4. 运行
	resMap, err := inference.Infer(tensorMap, outputMap)
	if err != nil {
		t.Errorf("failed to infer: %v", err)
	}

	for outName, tensor := range resMap {
		shape := tensor.GetShape()
		data := tensor.GetData()

		fmt.Printf("outName: %s, shape: %v, data: %v\n", outName, shape, data)
		tensor.Destroy()
	}
}
