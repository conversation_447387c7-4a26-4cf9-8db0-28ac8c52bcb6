package inference

import (
	"fmt"
	"strings"
	"testing"
	"time"

	wrapper "tokenizer_wrapper/tokenizer_wrapper"

	ort "github.com/yalue/onnxruntime_go"

	"golang.org/x/exp/constraints"
)

func max[T constraints.Ordered](a, b T) T {
	if a > b {
		return a
	}
	return b
}

func Preprocess(terms [][]string,simple_tokenzer,tokenizer *wrapper.TokenizerWrapper) (map[string][][]int64, [][][]float32) {
	lengthMax := 0
	batchSize := len(terms)

	queries := make([]string, batchSize)
	termsLength := make([][]int64, batchSize)

	for i, term := range terms {
		lengthMax = max(lengthMax, len(term))
		termsLength[i] = make([]int64, len(term))
		queries[i] = strings.Join(term, "")

		encodings, _ := simple_tokenzer.EncodeBatch(term, false)
		for j, encoding := range encodings {
			termsLength[i][j] = int64(len(encoding.Ids))
		}
	}

	// 构造term_t_lens
	termsTLens := make([][]int64, batchSize)
	for i := 0; i < batchSize; i++ {
		termsTLens[i] = make([]int64, lengthMax)
		for j := 0; j < lengthMax; j++ {
			termsTLens[i][j] = 1
		}
	}

	for i, lens := range termsLength {
		copy(termsTLens[i][:len(lens)], lens)
	}

	// 构造 input_ids, input_mask, token_type_ids
	encodings, _ := tokenizer.EncodeBatch(queries, true)
	ids := make([][]int64, len(encodings))
	masks := make([][]int64, len(encodings))
	types := make([][]int64, len(encodings))

	for i, encoding := range encodings {
		ids[i] = encoding.Ids
		masks[i] = encoding.Masks
		types[i] = encoding.Types
	}

	seqLength := len(ids[0])

	// 构造terms_mask
	termsMask := make([][][]float32, batchSize)
	for i := 0; i < batchSize; i++ {
		termsMask[i] = make([][]float32, lengthMax)
		for j := 0; j < lengthMax; j++ {
			termsMask[i][j] = make([]float32, seqLength)
		}
	}

	for i := 0; i < batchSize; i++ {
		lens := termsLength[i]
		var start int64 = 1
		for j, l := range lens {
			for k := start; k < start+l; k++ {
				termsMask[i][j][k] = 1
			}
			start += l
		}
	}

	resMap := map[string][][]int64{
		"input_ids":      ids,
		"input_mask":     masks,
		"token_type_ids": types,
		"terms_t_lens":   termsTLens,
	}

	return resMap, termsMask
}

func TestTermWeight(t *testing.T) {
	// 1. 初始化ort
	deviceId := -1
	onnxPath := "./onnx/twbert.onnx"
	libPath := "./lib/libonnxruntime.so"

	inference := NewInference(libPath, onnxPath, deviceId)
	if inference == nil {
		t.Errorf("failed to create inference")
	}
	defer inference.Destroy()

	modelName := "Xenova/ernie-3.0-nano-zh"
	useLocal, padId, padFixed, maxLength := 0, 0, -1, 32
	simple_tokenzer := wrapper.NewSimpleTokenizer(modelName, useLocal)
	tokenizer := wrapper.NewTokenizerWrapper(modelName, 0, padId, padFixed, maxLength)

	// 2. tokenizer
	terms := [][]string{
		{"滨海新区融媒体中心编委会", "副主任", "新媒体", "编辑部", "主任", "冯伟"},
		{"小学", "信息", "科技", "学科", "跨学科", "融合", "举例子"},
		{"湖南", "郴州", "张逸宽", "谁"},
	}

	tokenMap, termsMask := Preprocess(terms, simple_tokenzer, tokenizer)

	now := time.Now()
	// 3. 转tensor
	intputIdsTensor, _ := NewTensorFrom[int64](tokenMap["input_ids"])
	inputMaskTensor, _ := NewTensorFrom[int64](tokenMap["input_mask"])
	tokenTypeIdsTensor, _ := NewTensorFrom[int64](tokenMap["token_type_ids"])
	termsTLensTensor, _ := NewTensorFrom[int64](tokenMap["terms_t_lens"])
	termsMaskTensor, _ := NewTensorFrom[float32](termsMask)

	// 4. 组装
	tensorMap := map[string]ort.Value{
		"input_ids":      intputIdsTensor,
		"input_mask":     inputMaskTensor,
		"token_type_ids": tokenTypeIdsTensor,
		"terms_t_lens":   termsTLensTensor,
		"terms_mask":     termsMaskTensor,
	}

	batchSize, term_num := 3, 7
	outputMap := map[string][]int64{
		"term_weights": {int64(batchSize), int64(term_num)},
	}
	
	fmt.Printf("tokenizer cost: %dms\n", time.Since(now).Milliseconds())

	// 3. 运行
	resMap, err := inference.Infer(tensorMap, outputMap)
	if err != nil {
		t.Errorf("failed to infer: %v", err)
	}

	resTensor := resMap["term_weights"]
	resArray := NewArray(resTensor.GetData(), resTensor.GetShape())

	sum := make([]float32, batchSize)
	for i := 0; i < batchSize; i++ {
		for j := 0; j < term_num; j++ {
			sum[i] += resArray.Get(i, j)
		}
	}
	for i, term := range terms {
		for j, t := range term {
			fmt.Printf("%s: %f\n", t, resArray.Get(i, j)/sum[i])
		}
		fmt.Println("-------------")
	}
	fmt.Printf("cost: %dms\n", time.Since(now).Milliseconds())
}
