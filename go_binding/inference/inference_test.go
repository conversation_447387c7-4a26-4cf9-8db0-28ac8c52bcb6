package inference

import (
	"fmt"
	"testing"

	wrapper "tokenizer_wrapper/tokenizer_wrapper"

	ort "github.com/yalue/onnxruntime_go"
)

func TestNewInference(t *testing.T) {
	deviceId := 0
	onnxPath := "./onnx/twbert.onnx"
	libPath := "./lib/libonnxruntime.so"

	inference := NewInference(libPath, onnxPath, deviceId)
	if inference == nil {
		t.<PERSON>rrorf("failed to create inference")
	}
}

func TestInfer(t *testing.T) {
	// 1. 初始化ort
	deviceId := 1
	onnxPath := "./onnx/model.onnx"
	libPath := "./lib/libonnxruntime.so"

	inference := NewInference(libPath, onnxPath, deviceId)
	if inference == nil {
		t.Errorf("failed to create inference")
	}
	defer inference.Destroy()

	// 2. 初始化tokenizer
	modelName := "Xenova/ernie-3.0-nano-zh"
	useLocal, padId, padFixed, maxLength := 0, 0, 0, 512
	tokenizer := wrapper.NewTokenizerWrapper(modelName, useLocal, padId, padFixed, maxLength)

	queries := []string{"白日依山尽", "黄河入海流dddd", "欲穷千里目啦"}

	encodings, _ := tokenizer.EncodeBatch(queries, true)
	ids := make([][]int64, len(encodings))
	masks := make([][]int64, len(encodings))
	types := make([][]int64, len(encodings))

	for i, encoding := range encodings {
		ids[i] = encoding.Ids
		masks[i] = encoding.Masks
		types[i] = encoding.Types
	}

	// 3. 创建tensor
	idsTensor, _ := NewTensorFrom[int64](ids)
	masksTensor, _ := NewTensorFrom[int64](masks)
	typesTensor, _ := NewTensorFrom[int64](types)

	tensorMap := map[string]ort.Value{
		"input_ids":      idsTensor,
		"attention_mask": masksTensor,
		"token_type_ids": typesTensor,
	}

	batchSize := int64(len(queries))

	outputMap := map[string][]int64{
		"output": {batchSize, 2},
		"562":    {batchSize, 2},
	}

	// 4. 运行
	resMap, err := inference.Infer(tensorMap, outputMap)
	if err != nil {
		t.Errorf("failed to infer: %v", err)
	}

	for outName, tensor := range resMap {
		shape := tensor.GetShape()
		data := tensor.GetData()

		fmt.Printf("outName: %s, shape: %v, data: %v\n", outName, shape, data)
		tensor.Destroy()
	}
}
