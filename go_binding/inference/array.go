package inference

type Array[T any] struct {
	data  []T
	count  []int64
	shape []int64
}

func NewArray[T any](data []T, shape []int64) *Array[T] {
	dim := len(shape)
	cout := make([]int64, dim)
	cout[dim-1] = 1

	for i := dim - 2; i >= 0; i-- {
		cout[i] = cout[i+1] * shape[i+1]
	}

	return &Array[T]{
		data:  data,
		count:  cout,
		shape: shape,
	}
}

func (a *Array[T]) Get(index ...int) T {
	var ptr int64

	for i := 0; i < len(index); i++ {
		ptr += int64(index[i]) * a.count[i]
	}

	return a.data[ptr]
}
