package tokenizerwrapper

import (
	"fmt"
	"testing"
)

func TestEncodeBatch(t *testing.T) {
	modelName := "bert-base-uncased"
	useLocal, padId, padFixed, maxLength := 0, 0, 0, 512
	tokenizer := NewTokenizerWrapper(modelName, useLocal, padId, padFixed, maxLength)

	queries := []string{"白日依山尽", "欲穷千里目"}

	encodings, _ := tokenizer.EncodeBatch(queries, true)

	fmt.Printf("%+v\n", encodings)
}

func TestEncodeBatchPair(t *testing.T) {
	modelName := "/data/IflySearch_Resource/Rank/v20250325/jina-reranker-v2-base-multilingual_tokenizer/tokenizer.json"
	// modelName := "Xenova/ernie-3.0-nano-zh"
	// modelName := "bert-base-uncased"
	// modelName := "BAAI/bge-m3"
	// modelName := "BAAI/bge-base-zh-v1.5"
	useLocal, padId, padFixed, maxLength := 1, 0, 0, 512
	tokenizer := NewTokenizerWrapper(modelName, useLocal, padId, padFixed, maxLength)

	queries := []string{"一丁七万", "欲穷千里目"}
	answers := []string{"黄河入海流", "更上一层楼"}

	encodings, _ := tokenizer.EncodeBatchPair(queries, answers, true)

	fmt.Printf("%+v\n", encodings)

	encodings, _ = tokenizer.EncodeBatch(queries, true)
	fmt.Printf("%+v\n", encodings)
}

func TestSimple(t *testing.T) {
	modelName := "Xenova/ernie-3.0-nano-zh"
	// modelName := "/root/.cache/huggingface/hub/models--Xenova--ernie-3.0-nano-zh/snapshots/1e20da9955e7259c87019039c5b55dd1d0fcfee1/tokenizer.json"
	tokenizer := NewSimpleTokenizer(modelName, 0)
	tokenizer.WithPadding(0, 1, 15)
	tokenizer.WithTruncation(5)

	queries := []string{"东风夜放花千树", "更吹落"}
	answers := []string{"星如雪", "更上一层楼"}

	encodings, _ := tokenizer.EncodeBatchPair(queries, answers, true)

	fmt.Printf("%+v\n", encodings)
}
