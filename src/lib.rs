use std::ffi::{CStr, CString};
use std::os::raw::{c_char, c_int, c_uint};
use tokenizers::tokenizer::{PaddingParams, Tokenizer, TruncationParams};
use tokenizers::utils::padding::PaddingStrategy;

#[repr(C)]
pub struct TokenizerWrapper {
    tokenizer: Tokenizer,
}

#[no_mangle]
pub unsafe extern "C" fn create_simple_tokenizer(
    model_name: *const c_char,
    use_local: c_int,
) -> *mut TokenizerWrapper {
    let use_local = use_local != 0;
    let model_name = unsafe { CStr::from_ptr(model_name).to_str().unwrap() };

    let tokenizer: Option<Tokenizer> = if use_local {
        Tokenizer::from_file(model_name).ok()
    } else {
        Tokenizer::from_pretrained(model_name, None).ok()
    };

    match tokenizer {
        Some(tokenizer) => {
            let tokenizer_wrapper = TokenizerWrapper { tokenizer };
            Box::into_raw(Box::new(tokenizer_wrapper))
        }
        None => std::ptr::null_mut(),
    }
}

#[no_mangle]
pub unsafe extern "C" fn with_padding(
    wrapper: *mut TokenizerWrapper,
    pad_id: c_uint,
    pad_fix: c_int,
    max_length: usize,
) -> *mut TokenizerWrapper {
    let pad_fix = pad_fix != 0;

    let padding_params = PaddingParams {
        pad_id,
        strategy: if pad_fix {
            PaddingStrategy::Fixed(max_length)
        } else {
            PaddingStrategy::BatchLongest
        },
        ..Default::default()
    };

    let wrapper = unsafe { &mut *wrapper };
    wrapper.tokenizer.with_padding(Some(padding_params));
    wrapper
}

#[no_mangle]
pub unsafe extern "C" fn with_truncation(
    wrapper: *mut TokenizerWrapper,
    max_length: usize,
) -> *mut TokenizerWrapper {
    let wrapper = unsafe { &mut *wrapper };
    wrapper
        .tokenizer
        .with_truncation(Some(TruncationParams {
            max_length,
            ..Default::default()
        }))
        .unwrap();

    wrapper
}

#[no_mangle]
pub unsafe extern "C" fn create_tokenizer(
    model_name: *const c_char,
    use_local: c_int,
    pad_id: c_uint,
    pad_fix: c_int,
    max_length: usize,
) -> *mut TokenizerWrapper {
    let model_name = unsafe { CStr::from_ptr(model_name).to_str().unwrap() };
    let use_local = use_local != 0;
    let pad_fix = pad_fix != 0;

    let padding_params = PaddingParams {
        pad_id,
        strategy: if pad_fix {
            PaddingStrategy::Fixed(max_length)
        } else {
            PaddingStrategy::BatchLongest
        },
        ..Default::default()
    };

    let truncation_params = TruncationParams {
        max_length,
        ..Default::default()
    };

    if use_local {
        match Tokenizer::from_file(model_name) {
            Ok(mut tokenizer) => {
                tokenizer.with_padding(Some(padding_params));
                tokenizer.with_truncation(Some(truncation_params)).unwrap();
                Box::into_raw(Box::new(TokenizerWrapper { tokenizer }))
            }
            Err(e) => {
                eprintln!("Error creating tokenizer: {:?}", e);
                std::ptr::null_mut()
            }
        }
    } else {
        match Tokenizer::from_pretrained(model_name, None) {
            Ok(mut tokenizer) => {
                tokenizer.with_padding(Some(padding_params));
                tokenizer.with_truncation(Some(truncation_params)).unwrap();
                Box::into_raw(Box::new(TokenizerWrapper { tokenizer }))
            }
            Err(e) => {
                eprintln!("Error creating tokenizer: {:?}", e);
                std::ptr::null_mut()
            }
        }
    }
}

#[no_mangle]
pub unsafe extern "C" fn encode_batch(
    wrapper: *mut TokenizerWrapper,
    queries: *const *const c_char,
    query_len: usize,
    add_special_tokens: c_int,
) -> *mut c_char {
    let wrapper = unsafe { &mut *wrapper };
    let queries: Vec<&str> = unsafe {
        std::slice::from_raw_parts(queries, query_len)
            .iter()
            .map(|&s| CStr::from_ptr(s).to_str().unwrap())
            .collect()
    };

    match wrapper
        .tokenizer
        .encode_batch(queries, add_special_tokens != 0)
    {
        Ok(encodings) => {
            let json = serde_json::to_string(&encodings).unwrap();
            CString::new(json).unwrap().into_raw()
        }
        Err(e) => {
            eprintln!("Error encoding batch: {:?}", e);
            std::ptr::null_mut()
        }
    }
}

#[no_mangle]
pub unsafe extern "C" fn encode_batch_pair(
    wrapper: *mut TokenizerWrapper,
    queries: *const *const c_char,
    query_len: usize,
    answers: *const *const c_char,
    answer_len: usize,
    add_special_tokens: c_int,
) -> *mut c_char {
    let wrapper = unsafe { &mut *wrapper };
    let queries: Vec<&str> = unsafe {
        std::slice::from_raw_parts(queries, query_len)
            .iter()
            .map(|&s| CStr::from_ptr(s).to_str().unwrap())
            .collect()
    };

    let answers: Vec<&str> = unsafe {
        std::slice::from_raw_parts(answers, answer_len)
            .iter()
            .map(|&s| CStr::from_ptr(s).to_str().unwrap())
            .collect()
    };

    let texts: Vec<(&str, &str)> = queries
        .iter()
        .zip(answers.iter())
        .map(|(&a, &b)| (a, b))
        .collect();

    match wrapper
        .tokenizer
        .encode_batch(texts, add_special_tokens != 0)
    {
        Ok(encodings) => {
            let json = serde_json::to_string(&encodings).unwrap();
            CString::new(json).unwrap().into_raw()
        }
        Err(e) => {
            eprintln!("Error encoding batch: {:?}", e);
            std::ptr::null_mut()
        }
    }
}

#[no_mangle]
pub unsafe extern "C" fn free_string(s: *mut c_char) {
    if s.is_null() {
        return; // 检查空指针
    }
    unsafe {
        // 释放内存
        // 确保 s 是通过 CString::into_raw 创建的
        drop(CString::from_raw(s));
    }
}
