pub mod tokenizer_wrapper;

use tokenizer_wrapper::TokenizerWrapper;
use tokenizers::tokenizer::Result;

fn main() -> Result<()> {
    let mut tokenizer_wrapper = TokenizerWrapper::new_simple("/feng/Projects/RustProjects/tokenizer_wrapper/python/tokenizer/ernie-3.0-nano-zh/tokenizer.json", true)?;

    let encodings = tokenizer_wrapper.encode_batch(
        [
            "白\u{0000}日依山尽",
            "黄河入海流",
            "欲穷千里目",
            "更上一层楼",
        ]
        .to_vec(),
        true,
    )?;

    for encoding in encodings.iter() {
        println!("ids: {:?}", encoding.ids);
        println!("attention_mask: {:?}", encoding.attention_mask);
        println!("type_ids: {:?}", encoding.type_ids);
    }

    let json = serde_json::to_string(&encodings).unwrap();
    println!("{}", json);

    let encodings = tokenizer_wrapper.encode_batch_pair(
        vec!["白日依山尽", "欲穷千里目"],
        vec!["黄河入海流", "更上一层楼"],
        true,
    )?;

    for encoding in encodings.iter() {
        println!("ids: {:?}", encoding.ids);
        println!("attention_mask: {:?}", encoding.attention_mask);
        println!("type_ids: {:?}", encoding.type_ids);
    }

    Ok(())
}
