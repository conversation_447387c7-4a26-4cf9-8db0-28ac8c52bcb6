use serde::Serialize;
use tokenizers::{
    tokenizer::{PaddingParams, Result, Tokenizer, TruncationParams},
    utils::padding::PaddingStrategy,
    Encoding,
};

pub struct TokenizerWrapper {
    tokenizer: Tokenizer,
}

// 新增：只包含需要字段的结构体
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize)]
pub struct SimpleEncoding {
    pub ids: Vec<u32>,
    pub type_ids: Vec<u32>,
    pub attention_mask: Vec<u32>,
}

impl From<&Encoding> for SimpleEncoding {
    fn from(enc: &Encoding) -> Self {
        Self {
            ids: enc.get_ids().to_vec(),
            type_ids: enc.get_type_ids().to_vec(),
            attention_mask: enc.get_attention_mask().to_vec(),
        }
    }
}

impl TokenizerWrapper {
    pub fn new_simple(model_name: &str, use_local: bool) -> Result<Self> {
        let tokenizer = if use_local {
            Tokenizer::from_file(model_name)?
        } else {
            Tokenizer::from_pretrained(model_name, None)?
        };
        Ok(TokenizerWrapper { tokenizer })
    }

    pub fn new(model_name: &str, pad_id: u32, pad_fix: bool, max_length: usize) -> Result<Self> {
        let padding_params = PaddingParams {
            pad_id,
            strategy: if pad_fix {
                PaddingStrategy::Fixed(max_length)
            } else {
                PaddingStrategy::BatchLongest
            },
            ..Default::default()
        };

        let truncation_params = TruncationParams {
            max_length,
            ..Default::default()
        };

        let mut tokenizer = Tokenizer::from_pretrained(model_name, None)?;
        tokenizer.with_padding(Some(padding_params));
        tokenizer.with_truncation(Some(truncation_params))?;

        Ok(TokenizerWrapper { tokenizer })
    }

    pub fn encode_batch(
        &mut self,
        queries: Vec<&str>,
        add_special_tokens: bool,
    ) -> Result<Vec<SimpleEncoding>> {
        let encodings = self.tokenizer.encode_batch(queries, add_special_tokens)?;
        Ok(encodings.iter().map(SimpleEncoding::from).collect())
    }

    pub fn encode_batch_pair(
        &mut self,
        queries: Vec<&str>,
        answers: Vec<&str>,
        add_special_tokens: bool,
    ) -> Result<Vec<SimpleEncoding>> {
        let texts: Vec<(&str, &str)> = queries
            .iter()
            .zip(answers.iter())
            .map(|(&a, &b)| (a, b))
            .collect();
        let encodings = self.tokenizer.encode_batch(texts, add_special_tokens)?;
        Ok(encodings.iter().map(SimpleEncoding::from).collect())
    }
}
